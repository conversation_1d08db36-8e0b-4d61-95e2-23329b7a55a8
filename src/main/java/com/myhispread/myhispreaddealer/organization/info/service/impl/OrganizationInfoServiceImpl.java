package com.myhispread.myhispreaddealer.organization.info.service.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.myhispread.myhispreaddealer.common.constant.ErrorCodeEnum;
import com.myhispread.myhispreaddealer.common.constant.SystemConstant;
import com.myhispread.myhispreaddealer.common.exception.CognitionException;
import com.myhispread.myhispreaddealer.common.response.organization.OrganizationInfoResponse;
import com.myhispread.myhispreaddealer.common.result.Result;
import com.myhispread.myhispreaddealer.common.snowflake.SnowflakeIdGenerator;
import com.myhispread.myhispreaddealer.common.utils.BillingUtils;
import com.myhispread.myhispreaddealer.common.utils.SystemClock;
import com.myhispread.myhispreaddealer.common.utils.UserContextHolder;
import com.myhispread.myhispreaddealer.jpa.bind.organization.bean.UserOrganizationBind;
import com.myhispread.myhispreaddealer.jpa.bind.organization.repository.UserOrganizationBindRepository;
import com.myhispread.myhispreaddealer.jpa.organization.dailybill.repository.OrganizationLLMDailyBillRepository;
import com.myhispread.myhispreaddealer.jpa.organization.dailybill.repository.OrganizationSearchDailyBillRepository;
import com.myhispread.myhispreaddealer.jpa.organization.info.bean.OrganizationInfo;
import com.myhispread.myhispreaddealer.jpa.organization.info.repository.OrganizationInfoRepository;
import com.myhispread.myhispreaddealer.jpa.organization.secret.repository.OrganizationSecretRepository;
import com.myhispread.myhispreaddealer.jpa.organization.wallet.bean.OrganizationWallet;
import com.myhispread.myhispreaddealer.jpa.organization.wallet.bean.OrganizationWalletRecord;
import com.myhispread.myhispreaddealer.jpa.organization.wallet.repository.OrganizationWalletRecordRepository;
import com.myhispread.myhispreaddealer.jpa.organization.wallet.repository.OrganizationWalletRepository;
import com.myhispread.myhispreaddealer.organization.info.service.OrganizationInfoService;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.*;

@Slf4j
@Service
@RequiredArgsConstructor
public class OrganizationInfoServiceImpl implements OrganizationInfoService {

    private final ObjectMapper objectMapper;

    private final RedisTemplate<String, String> redisTemplate;

    private final SnowflakeIdGenerator snowflakeIdGenerator;

    private final OrganizationInfoRepository organizationInfoRepository;

    private final UserOrganizationBindRepository userOrganizationBindRepository;

    private final OrganizationSecretRepository organizationSecretRepository;

    private final OrganizationWalletRepository organizationWalletRepository;

    private final OrganizationLLMDailyBillRepository organizationLLMDailyBillRepository;

    private final OrganizationSearchDailyBillRepository organizationSearchDailyBillRepository;

    private final OrganizationWalletRecordRepository organizationWalletRecordRepository;


    @Override
    @Transactional(readOnly = true)
    public Result<List<OrganizationInfoResponse>> selectOrganizationInfoAll(String zoneId) {
        long userId = UserContextHolder.getUserContext().getId();
        List<UserOrganizationBind> binds = userOrganizationBindRepository
                .findUserOrganizationBindsByUserIdAndStatuses(userId, SystemConstant.AVAILABLE);

        if (binds.isEmpty()) {
            return new Result<List<OrganizationInfoResponse>>().success(Collections.emptyList());
        }

        List<OrganizationInfoResponse> organizationInfoResponseList = new ArrayList<>();
        Iterator<UserOrganizationBind> userOrganizationBindIterator = binds.iterator();
        while (userOrganizationBindIterator.hasNext()) {
            UserOrganizationBind userOrganizationBind = userOrganizationBindIterator.next();
            OrganizationInfo organizationInfo = organizationInfoRepository.findOrganizationInfoById(userOrganizationBind.getOrganizationId());
            Integer secretNumber = organizationSecretRepository.countOrganizationSecretsByOrganizationIdAndStatuses(organizationInfo.getId(), SystemConstant.AVAILABLE);
            OrganizationWallet organizationWallet = organizationWalletRepository.findOrganizationWalletByOrganizationIdAndStatuses(organizationInfo.getId(), SystemConstant.AVAILABLE);
            ZoneId tz = ZoneId.of(zoneId);

            LocalDate monthStartLocal = LocalDate.now(tz).withDayOfMonth(1);
            LocalDate nextMonthStart  = monthStartLocal.plusMonths(1);

            long monthStartUtc = monthStartLocal.atStartOfDay(tz)
                    .toInstant()
                    .toEpochMilli();
            long nextMonthUtc  = nextMonthStart.atStartOfDay(tz)
                    .toInstant()
                    .toEpochMilli();

            BigDecimal llmQuota = organizationLLMDailyBillRepository.sumQuotaInMonth(organizationInfo.getId(), monthStartUtc, nextMonthUtc);
            BigDecimal quota = llmQuota.add(organizationSearchDailyBillRepository.sumQuotaInMonth(organizationInfo.getId(), monthStartUtc, nextMonthUtc));
            OrganizationInfoResponse organizationInfoResponse = new OrganizationInfoResponse(organizationInfo.getId(), organizationInfo.getOrganizationName(), (organizationWallet.getTotalAmount()-organizationWallet.getUsedAmount()), BillingUtils.usdToNanoCent(quota), organizationWallet.getUsedAmount(), secretNumber, organizationInfo.getStatuses());
            organizationInfoResponseList.add(organizationInfoResponse);
        }
        return new Result<List<OrganizationInfoResponse>>().success(organizationInfoResponseList);
    }

    @SneakyThrows
    @Override
    @Transactional
    public Result<OrganizationInfo> persistenceOrganizationInfo(Long userId, String organizationName) {
        Long time = SystemClock.now();
        OrganizationInfo organizationInfo = new OrganizationInfo(snowflakeIdGenerator.nextId(), SystemConstant.LEVE_ZERO, organizationName, time, SystemConstant.AVAILABLE);
        UserOrganizationBind userOrganizationBind = new UserOrganizationBind(snowflakeIdGenerator.nextId(), UserContextHolder.getUserContext().getId(), organizationInfo.getId(), time, SystemConstant.AVAILABLE, SystemConstant.AVAILABLE);
        OrganizationWallet organizationWallet = new OrganizationWallet(snowflakeIdGenerator.nextId(), organizationInfo.getId(), BillingUtils.usdToNanoCent(BigDecimal.valueOf(5L)), 0L, 0L, 0L, time, SystemConstant.AVAILABLE);
        OrganizationWalletRecord organizationWalletRecord = new OrganizationWalletRecord(snowflakeIdGenerator.nextId(), organizationInfo.getId(), organizationWallet.getId(), BillingUtils.usdToNanoCent(BigDecimal.valueOf(5L)), SystemConstant.UNAVAILABLE, time, SystemConstant.AVAILABLE);
        organizationInfoRepository.save(organizationInfo);
        userOrganizationBindRepository.save(userOrganizationBind);
        organizationWalletRepository.save(organizationWallet);
        organizationWalletRecordRepository.save(organizationWalletRecord);
        redisTemplate.opsForValue().set(SystemConstant.ORGANIZATION_REDIS_ID + organizationInfo.getId(), objectMapper.writeValueAsString(Map.of("code", 200, "level", organizationInfo.getLevel())));
        redisTemplate.opsForValue().set(SystemConstant.ORGANIZATION_REDIS_BALANCE + organizationInfo.getId(), String.valueOf(BillingUtils.usdToNanoCent(BigDecimal.valueOf(5L))));
        return new Result<OrganizationInfo>().success(organizationInfo);
    }


    @Override
    public Result<String> updateOrganizationInfo(Long organizationId, String organizationName) {
        OrganizationInfo organizationInfo = organizationInfoRepository.findOrganizationInfoById(organizationId);
        organizationInfo.setOrganizationName(organizationName);
        organizationInfoRepository.save(organizationInfo);
        return new Result<String>().success(ErrorCodeEnum.SUCCESS);

    }

    @Override
    public Result<String> deleteOrganizationInfo(Long organizationId) {
        Result<String> result = new Result<>();
        OrganizationInfo organizationInfo = organizationInfoRepository.findOrganizationInfoById(organizationId);
        if (null != organizationInfo) {
            organizationInfo.setStatuses(SystemConstant.UNAVAILABLE);
            organizationInfoRepository.saveAndFlush(organizationInfo);
            result.setDesc("订阅删除成功");
        } else {
            result.setDesc("订阅不存在，无法删除");
        }

        return result;
    }

    @Override
    public Result<OrganizationInfoResponse> currentOrganization(Long userId, String zoneId) {
        Long organizationId = getOrganizationId(userId);
        OrganizationInfo organizationInfo = organizationInfoRepository.findOrganizationInfoById(organizationId);
        return Result.successResult(buildOrganizationInfoResponse(organizationInfo, zoneId));
    }

    @Override
    public Result<OrganizationInfoResponse> changeCurrentOrganization(Long newOrganizationId, Long userId, String zoneId) {
        List<UserOrganizationBind> binds = userOrganizationBindRepository
                .findUserOrganizationBindsByUserIdAndStatuses(userId, SystemConstant.AVAILABLE);
        if (binds.stream().map(UserOrganizationBind::getOrganizationId).noneMatch(newOrganizationId::equals)) {
            throw new CognitionException(ErrorCodeEnum.NO_PERMISSION);
        }
        redisTemplate.opsForValue().set(SystemConstant.USER_SELECTED_ORGANIZATION + userId, String.valueOf(newOrganizationId));
        OrganizationInfo organizationInfo = organizationInfoRepository.findOrganizationInfoById(newOrganizationId);
        return Result.successResult(buildOrganizationInfoResponse(organizationInfo, zoneId));
    }

    private Long getOrganizationId(Long userId) {
        String cachedOrgId = redisTemplate.opsForValue().get(SystemConstant.USER_SELECTED_ORGANIZATION + userId);

        if (cachedOrgId != null) {
            return Long.valueOf(cachedOrgId);
        }

        List<UserOrganizationBind> binds = userOrganizationBindRepository
                .findUserOrganizationBindsByUserIdAndStatuses(userId, SystemConstant.AVAILABLE);

        if (binds.isEmpty()) {
            throw new CognitionException(ErrorCodeEnum.ORGANIZATION_NOT_FOUND);
        }

        UserOrganizationBind userOrganizationBind = binds.getFirst();
        Long organizationId = userOrganizationBind.getOrganizationId();

        redisTemplate.opsForValue().set(
                SystemConstant.USER_SELECTED_ORGANIZATION + userId,
                String.valueOf(organizationId)
        );

        return organizationId;
    }

    private OrganizationInfoResponse buildOrganizationInfoResponse(OrganizationInfo organizationInfo, String zoneId) {
        Long orgId = organizationInfo.getId();

        Integer secretNumber = organizationSecretRepository
                .countOrganizationSecretsByOrganizationIdAndStatuses(orgId, SystemConstant.AVAILABLE);

        OrganizationWallet organizationWallet = organizationWalletRepository
                .findOrganizationWalletByOrganizationIdAndStatuses(orgId, SystemConstant.AVAILABLE);

        ZoneId tz = ZoneId.of(zoneId);

        LocalDate monthStartLocal = LocalDate.now(tz).withDayOfMonth(1);
        LocalDate nextMonthStart  = monthStartLocal.plusMonths(1);

        long monthStartUtc = monthStartLocal.atStartOfDay(tz)
                .toInstant()
                .toEpochMilli();
        long nextMonthUtc  = nextMonthStart.atStartOfDay(tz)
                .toInstant()
                .toEpochMilli();

        BigDecimal llmQuota = organizationLLMDailyBillRepository.sumQuotaInMonth(organizationInfo.getId(), monthStartUtc, nextMonthUtc);
        BigDecimal quota = llmQuota.add(organizationSearchDailyBillRepository.sumQuotaInMonth(organizationInfo.getId(), monthStartUtc, nextMonthUtc));

        return new OrganizationInfoResponse(
                orgId,
                organizationInfo.getOrganizationName(),
                organizationWallet.getTotalAmount(),
                BillingUtils.usdToNanoCent(quota),
                organizationWallet.getUsedAmount(),
                secretNumber,
                organizationInfo.getStatuses()
        );
    }
}
