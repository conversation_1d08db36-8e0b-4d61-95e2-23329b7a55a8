package com.myhispread.myhispreaddealer.organization.dailybill.service.impl;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.myhispread.myhispreaddealer.common.response.organization.OrganizationDailyBillResponse;
import com.myhispread.myhispreaddealer.common.result.Result;
import com.myhispread.myhispreaddealer.common.utils.TimerUtils;
import com.myhispread.myhispreaddealer.jpa.organization.cycle.repository.OrganizationDailyBillResponseRepository;
import com.myhispread.myhispreaddealer.jpa.organization.info.bean.OrganizationInfo;
import com.myhispread.myhispreaddealer.jpa.organization.info.repository.OrganizationInfoRepository;
import com.myhispread.myhispreaddealer.organization.dailybill.service.OrganizationDailyBillService;
import com.myhispread.myhispreaddealer.organization.info.service.OrganizationInfoService;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.TreeMap;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class OrganizationDailyBillServiceImpl implements OrganizationDailyBillService {

    private final OrganizationDailyBillResponseRepository organizationDailyBillResponseRepository;
    private final OrganizationInfoService organizationInfoService;
    private final OrganizationInfoRepository organizationInfoRepository;

    @Override
    public Result<Map<Long, List<OrganizationDailyBillResponse>>> showCharts(Integer selectedYear, Integer selectedMonth, String zoneId, Long organizationId) {
        ZoneId zone = ZoneId.of(zoneId);
        long[] range = TimerUtils.monthRange(selectedYear, selectedMonth, zone);

        List<OrganizationDailyBillResponse> list =
                organizationDailyBillResponseRepository.findBills(organizationId, range[0], range[1]);

        // 按 billDay 分组
        Map<Long, List<OrganizationDailyBillResponse>> groupedData = list.stream()
                .collect(Collectors.groupingBy(
                        OrganizationDailyBillResponse::getBillDay,
                        TreeMap::new,
                        Collectors.toList()));

        // 填充没有数据的日期，将所有值设为0
        fillMissingDays(groupedData, selectedYear, selectedMonth, zone, organizationId);

        return Result.successResult(groupedData);
    }

    @Override
    @SneakyThrows
    public void excelDownload(Integer selectedYear, Integer selectedMonth, String zoneId, Long organizationId, HttpServletResponse response) {
        ZoneId zone = ZoneId.of(zoneId);
        long[] range = TimerUtils.monthRange(selectedYear, selectedMonth, zone);
        
        // 定义开始和结束时间
        LocalDate startDate = LocalDate.of(selectedYear, selectedMonth, 1);
        LocalDate endDate = startDate.plusMonths(1).minusDays(1);
        
        List<OrganizationDailyBillResponse> list =
                organizationDailyBillResponseRepository.findBills(organizationId, range[0], range[1]);

        var organizationInfoById = organizationInfoRepository.findOrganizationInfoById(organizationId);
        if (organizationInfoById == null) {
            throw new RuntimeException("组织不存在");
        }
        // Set response headers
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");
        String dayBillFileName = "billing_" + organizationInfoById.getOrganizationName() + "_" + startDate.format(DateTimeFormatter.ISO_DATE) + "_to_" + endDate.format(DateTimeFormatter.ISO_DATE) + ".xlsx";

        String encodedFileName = URLEncoder.encode(dayBillFileName, StandardCharsets.UTF_8).replaceAll("\\+", "%20");
        response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + encodedFileName);
        response.setHeader("Access-Control-Expose-Headers", "Content-Disposition");


        // Write to Excel
        try (ExcelWriter excelWriter = EasyExcel.write(response.getOutputStream()).build()) {
            // Create sheet for LLM Bill
            WriteSheet llmWriteSheet = EasyExcel.writerSheet("Day Bill")
                    .head(OrganizationDailyBillResponse.class) // Specify DTO for headers
                    .build();
            excelWriter.write(list, llmWriteSheet);
        }
    }

    @Override
    @SneakyThrows
    public void downloadBatchHourlyCycle(Long organizationId, ZonedDateTime startTime, ZonedDateTime endTime, HttpServletResponse response) {
        // 获取组织信息
        var organizationInfo = organizationInfoRepository.findById(organizationId)
                .orElseThrow(() -> new IllegalArgumentException("Organization not found with id: " + organizationId));

        // 设置响应头
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");

        // 创建文件名
        String excelFileNameBase = "billing_" + organizationInfo.getOrganizationName() + "_"
                + startTime.format(DateTimeFormatter.ISO_LOCAL_DATE_TIME) + "_to_"
                + endTime.format(DateTimeFormatter.ISO_LOCAL_DATE_TIME) + ".xlsx";

        // URL编码文件名
        String encodedFileName = URLEncoder.encode(excelFileNameBase, StandardCharsets.UTF_8).replaceAll("\\+", "%20");
        response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + encodedFileName);
        response.setHeader("Access-Control-Expose-Headers", "Content-Disposition");

        // 获取账单数据
        var llmExcelDataList = charactersLLMCycleRepository.findCharactersLLMCycleDetailsByDateRange(organizationId, startTime, endTime);
        var searchExcelDataList = charactersSearchCycleRepository.findCharactersLLMSearchDetails(organizationId, startTime, endTime);

        // 使用EasyExcel写入数据到响应输出流
        try (ExcelWriter excelWriter = EasyExcel.write(response.getOutputStream()).build()) {
            // 创建LLM账单工作表
            WriteSheet llmWriteSheet = EasyExcel.writerSheet("LLM Bill")
                    .head(CharactersLLMCycleDetailDTO.class)
                    .build();
            excelWriter.write(llmExcelDataList, llmWriteSheet);

            // 创建Search账单工作表
            WriteSheet searchWriteSheet = EasyExcel.writerSheet("Search Bill")
                    .head(CharactersLLMSearchDetailDTO.class)
                    .build();
            excelWriter.write(searchExcelDataList, searchWriteSheet);
        } catch (IOException e) {
            log.error("Error creating Excel file for organizationId: {}", organizationId, e);
            // 根据实际需求，这里可以抛出自定义异常或让 @SneakyThrows 处理
            throw e;
        }
    }

    /**
     * 填充当月没有数据的日期，所有值设为0
     *
     * @param groupedData    已分组的数据
     * @param year           年份
     * @param month          月份
     * @param zone           时区
     * @param organizationId 组织ID
     */
    private void fillMissingDays(Map<Long, List<OrganizationDailyBillResponse>> groupedData,
                                 Integer year, Integer month, ZoneId zone, Long organizationId) {
        LocalDate firstDay = LocalDate.of(year, month, 1);
        int daysInMonth = firstDay.lengthOfMonth();

        // 遍历当月所有日期
        for (int day = 1; day <= daysInMonth; day++) {
            LocalDate currentDate = LocalDate.of(year, month, day);
            long currentDayMillis = currentDate.atStartOfDay(zone).toInstant().toEpochMilli();

            // 如果当前日期不存在数据，则添加零值数据
            if (!groupedData.containsKey(currentDayMillis)) {
                List<OrganizationDailyBillResponse> emptyList = new ArrayList<>();

                // 添加LLM类型的空记录
                emptyList.add(createEmptyBillResponse(currentDayMillis, organizationId, "LLM", 0));

                // 添加Search类型的空记录
                emptyList.add(createEmptyBillResponse(currentDayMillis, organizationId, "Search", 1));

                groupedData.put(currentDayMillis, emptyList);
            }
        }
    }

    /**
     * 创建一个所有值为0的账单响应对象
     *
     * @param billDay        账单日期时间戳
     * @param organizationId 组织ID
     * @param modelName      模型名称
     * @param modelType      模型类型 (0=LLM, 1=Search)
     * @return 空的账单响应对象
     */
    private OrganizationDailyBillResponse createEmptyBillResponse(Long billDay, Long organizationId,
                                                                  String modelName, Integer modelType) {
        OrganizationDailyBillResponse response = new OrganizationDailyBillResponse();
        response.setBillDay(billDay);
        response.setOrganizationId(organizationId);
        response.setModelName(modelName);
        response.setModelType(modelType);

        // 设置所有数值为0
        response.setTextPrompt(0L);
        response.setTextCachePrompt(0L);
        response.setTextCompletion(0L);
        response.setTextCachePromptWrite(0L);
        response.setAudioPrompt(0L);
        response.setAudioCachePrompt(0L);
        response.setAudioCompletion(0L);
        response.setReasoningCompletion(0L);
        response.setImagePrompt(0L);
        response.setImageCachePrompt(0L);
        response.setImageCompletion(0L);
        response.setCall(0L);
        response.setRequest(0L);
        response.setQuota(BigDecimal.ZERO);

        return response;
    }
}
