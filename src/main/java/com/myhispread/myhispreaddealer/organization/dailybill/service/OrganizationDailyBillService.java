package com.myhispread.myhispreaddealer.organization.dailybill.service;

import com.myhispread.myhispreaddealer.common.response.organization.OrganizationDailyBillResponse;
import com.myhispread.myhispreaddealer.common.result.Result;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.web.bind.annotation.RequestParam;

import java.time.ZonedDateTime;
import java.util.List;
import java.util.Map;

public interface OrganizationDailyBillService {
    public Result<Map<Long, List<OrganizationDailyBillResponse>>> showCharts(Integer selectedYear, Integer selectedMonth, String zoneId, Long organizationId);

    void excelDownload(Integer selectedYear, Integer selectedMonth, String zoneId, Long organizationId, HttpServletResponse response);

    void downloadBatchHourlyCycle(Long organizationId, ZonedDateTime startTime, ZonedDateTime endTime, HttpServletResponse response);
}
