package com.myhispread.myhispreaddealer.common.response.organization;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class OrganizationInfoResponse {

    private Long organizationId;

    private String organizationName;

    //钱包余额-redis
    private Long balance ;
    //当月用量
    private Long monthUsage;
    //累计使用量
    private Long accumulatedUsage;
    //可用key数量
    private Integer availableKeys;

    private Integer statuses;
}
