package com.myhispread.myhispreaddealer.jpa.organization.cycle.repository;

import com.myhispread.myhispreaddealer.jpa.organization.cycle.bean.CharactersLLMCycle;
import com.myhispread.myhispreaddealer.organization.dailybill.service.impl.CharactersLLMCycleDetailDTO;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.time.ZonedDateTime;
import java.util.List;

public interface CharactersLLMCycleRepository extends JpaRepository<CharactersLLMCycle, Long> {

    @Query("SELECT NEW com.myhispread.myhispreaddealer.organization.dailybill.service.impl.CharactersLLMCycleDetailDTO("
            + "c.serverName, c.cognitionModelName, c.createTime, c.quota, c.customizedQuota, "
            + "c.textCompletion, c.textPrompt, c.textCachePrompt, c.textCachePromptWrite, "
            + "c.audioCompletion, c.audioPrompt, c.audioCachePrompt, "
            + "c.imageCompletion, c.imagePrompt, c.imageCachePrompt, "
            + "c.reasoningCompletion, c.request, c.statuses, c.type, "
            + "c.audioCachePromptPrices, c.audioCompletionPrices, c.audioPromptPrices, "
            + "c.imageCachePromptPrices, c.imageCompletionPrices, c.imagePromptPrices, "
            + "c.reasoningCompletionPrices, c.textCachePromptPrices, c.textCachePromptWritePrices, "
            + "c.textCompletionPrices, c.textPromptPrices) "
            + "FROM CharactersLLMCycle c "
            + "WHERE c.organizationId = :organizationId AND c.createTime >= :startTime AND c.createTime <= :endTime")
    List<CharactersLLMCycleDetailDTO> findCharactersLLMCycleDetailsByDateRange(
            @Param("organizationId") Long organizationId,
            @Param("startTime") ZonedDateTime startTime,
            @Param("endTime") ZonedDateTime endTime);
}
