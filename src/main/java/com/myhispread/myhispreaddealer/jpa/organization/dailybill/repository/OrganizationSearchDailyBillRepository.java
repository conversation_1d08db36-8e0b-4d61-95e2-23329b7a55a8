package com.myhispread.myhispreaddealer.jpa.organization.dailybill.repository;

import com.myhispread.myhispreaddealer.jpa.organization.dailybill.bean.OrganizationSearchDailyBill;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.List;

@Repository
public interface OrganizationSearchDailyBillRepository extends JpaRepository<OrganizationSearchDailyBill, Long> {


    @Query(value = """
    SELECT COALESCE(SUM(quota), 0) AS total_quota
    FROM   sp_console_organization_search_daily_bill
    WHERE  organization_id = :orgId
      AND  bill_day >= :monthStartUtc
      AND  bill_day <  :nextMonthStartUtc
    """, nativeQuery = true)
    BigDecimal sumQuotaInMonth(@Param("orgId")             Long orgId,
                               @Param("monthStartUtc")     Long monthStartUtc,
                               @Param("nextMonthStartUtc") Long nextMonthStartUtc);

    @Query(value = """
    SELECT *
    FROM sp_console_organization_search_daily_bill
    WHERE organization_id = :orgId
      AND bill_day >= :startMillis
      AND bill_day <  :endMillis
    """, nativeQuery = true)
    List<OrganizationSearchDailyBill> findSearchBillByRange(
            @Param("orgId")       Long   orgId,
            @Param("startMillis") Long   startMillis,
            @Param("endMillis")   Long   endMillis);


    @Query(value = """
    /* ---------- 1) 锁定欲更新行（若存在则返回 id） ---------- */
    WITH target AS (
        SELECT id
        FROM   sp_console_organization_search_daily_bill
        WHERE  organization_id = :orgId
          AND  model_name      = :modelName
          AND  bill_day        = :dayStart
        ORDER  BY id          -- 如有多条，只更新最早一条
        LIMIT  1
        FOR UPDATE
    ),
    /* ---------- 2) 增量 UPDATE，若无行则返回 0 行 ---------- */
    upd AS (
        UPDATE sp_console_organization_search_daily_bill AS db
        SET    create_time      = :nowMillis,
               request          = db.request + :req,
               call             = db.call    + :callCnt,
               quota            = db.quota   + :quota,
               customized_quota = db.customized_quota + :cquota
        WHERE  db.id IN (SELECT id FROM target)
        RETURNING id           -- 告诉后面有没有命中
    )
    /* ---------- 3) 若 UPDATE 未命中，则插入新行 ---------- */
    INSERT INTO sp_console_organization_search_daily_bill
    (id, organization_id, model_name, bill_day, create_time,
     request, call, quota, customized_quota)
    SELECT :id, :orgId, :modelName, :dayStart, :nowMillis,
           :req, :callCnt, :quota, :cquota
    WHERE NOT EXISTS (SELECT 1 FROM upd);
    """, nativeQuery = true)
    void upsertDaily(@Param("id")        Long       id,         // 雪花主键，仅插入时用
                     @Param("orgId")     Long       orgId,
                     @Param("modelName") String     modelName,
                     @Param("dayStart")  Long       dayStart,
                     @Param("nowMillis") Long       nowMillis,
                     @Param("req")       Long       req,
                     @Param("callCnt")   Long       callCnt,
                     @Param("quota")     BigDecimal quota,
                     @Param("cquota")    BigDecimal cquota);


}
