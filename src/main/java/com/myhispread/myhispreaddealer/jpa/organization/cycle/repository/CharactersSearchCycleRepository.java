package com.myhispread.myhispreaddealer.jpa.organization.cycle.repository;

import com.myhispread.myhispreaddealer.jpa.organization.cycle.bean.CharactersSearchCycle;
import com.myhispread.myhispreaddealer.organization.dailybill.service.impl.CharactersLLMSearchDetailDTO;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.time.ZonedDateTime;
import java.util.List;

public interface CharactersSearchCycleRepository extends JpaRepository<CharactersSearchCycle, Long> {

    @Query("SELECT NEW com.myhispread.myhispreaddealer.organization.dailybill.service.impl.CharactersLLMSearchDetailDTO("
            + "c.serverName, c.cognitionModelName, c.createTime, c.quota, c.customizedQuota, "
            + "c.callCount, c.requestCount, c.callPrices) " // Assuming entity fields are callCount and requestCount for DTO fields call and request
            + "FROM CharactersSearchCycle c "
            + "WHERE c.organizationId = :organizationId AND c.createTime >= :startTime AND c.createTime <= :endTime")
    List<CharactersLLMSearchDetailDTO> findCharactersLLMSearchDetails(
            @Param("organizationId") Long organizationId,
            @Param("startTime") ZonedDateTime startTime,
            @Param("endTime") ZonedDateTime endTime);
}
