package com.myhispread.myhispreaddealer.jpa.organization.dailybill.repository;

import com.myhispread.myhispreaddealer.jpa.organization.dailybill.bean.OrganizationLLMDailyBill;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.List;

@Repository
public interface OrganizationLLMDailyBillRepository extends JpaRepository<OrganizationLLMDailyBill, Long> {



    @Query(value = """
    SELECT COALESCE(SUM(quota), 0) AS total_quota
    FROM   sp_console_organization_llm_daily_bill
    WHERE  organization_id = :orgId
      AND  bill_day >= :monthStartUtc
      AND  bill_day <  :nextMonthStartUtc
    """, nativeQuery = true)
    BigDecimal sumQuotaInMonth(@Param("orgId")             Long orgId,
                               @Param("monthStartUtc")     Long monthStartUtc,
                               @Param("nextMonthStartUtc") Long nextMonthStartUtc);

}
