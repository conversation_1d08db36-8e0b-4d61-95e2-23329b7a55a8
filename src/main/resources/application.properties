spring.application.name=myhispread-dealer


spring.lifecycle.timeout-per-shutdown-phase=60S
spring.threads.virtual.enabled=true
spring.jackson.time-zone=UTC

server.port=8080
server.shutdown=graceful
server.max-http-request-header-size=8196
server.servlet.encoding.charset=UTF-8
server.servlet.encoding.force=true
server.servlet.encoding.enabled=true

spring.data.redis.timeout=5000
spring.data.redis.connect-timeout=5000
spring.data.redis.repositories.enabled=false
spring.data.redis.lettuce.pool.enabled=true
spring.data.redis.lettuce.pool.max-active=2000
spring.data.redis.lettuce.pool.max-wait=-1
spring.data.redis.lettuce.pool.max-idle=1000
spring.data.redis.lettuce.pool.min-idle=100

spring.datasource.driver-class-name=org.postgresql.Driver
spring.datasource.hikari.auto-commit=true
spring.datasource.hikari.pool-name=pgsql
spring.datasource.hikari.minimum-idle=100
spring.datasource.hikari.maximum-pool-size=2000
spring.datasource.hikari.connection-timeout=10000
spring.datasource.hikari.allow-pool-suspension=false
spring.datasource.hikari.isolate-internal-queries=false
spring.datasource.hikari.read-only=false
spring.datasource.hikari.register-mbeans=false
spring.datasource.hikari.validation-timeout=1000
spring.datasource.hikari.leak-detection-threshold=500000

spring.jpa.show-sql=false
spring.jpa.open-in-view=false
spring.jpa.properties.hibernate.hbm2ddl.auto=none
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.PostgreSQLDialect
spring.jpa.hibernate.naming.physical-strategy=org.hibernate.boot.model.naming.CamelCaseToUnderscoresNamingStrategy
spring.jpa.properties.hibernate.jdbc.batch_size=1000
spring.jpa.properties.hibernate.order_inserts=true
spring.jpa.properties.hibernate.order_updates=true
spring.jpa.properties.hibernate.cache.use_second_level_cache=false
spring.jpa.properties.hibernate.cache.use_query_cache=false
spring.jpa.properties.jakarta.persistence.sharedCache.mode=NONE
spring.jpa.properties.hibernate.jdbc.time_zone=UTC



#debug=true
logging.level.org.springframework.security=INFO

spring.scheduler.enabled=true
resend.key=re_XYhSxvAd_BttAtGooU8R29YiCVhxDExUn
email.callback=http://localhost:5173/login


spring.data.redis.url=redis://<EMAIL>:10001
#spring.data.redis.url=redis://<EMAIL>:22789

spring.datasource.url=***************************************************************************************************
#spring.datasource.url=**************************************************************************************************
spring.datasource.username=hispread
spring.datasource.password=Hispread0719.


jwt.secret=fRwKqasOY6UuI3/7a5aVl+4FFFZgaKtEbnWw19m66qm7qXdMHnLROB+DdpyA3UEPN9DR/37dKGxTSyd9v66n5A==
jwt.expiration=86400